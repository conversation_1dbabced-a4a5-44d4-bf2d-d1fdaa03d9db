{"expo": {"name": "BCS-Question-Bank", "slug": "targetBCS", "version": "6.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "bcsquestionbank", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "bollywood.fm11"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "bollywood.fm11"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["react-native-google-mobile-ads", {"androidAppId": "ca-app-pub-****************~**********", "iosAppId": "ca-app-pub-xxxxxxxx~xxxxxxxx"}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "74b4d20c-9de3-423a-bd04-28a6c830d6a4"}}}}