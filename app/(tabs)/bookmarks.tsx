import { ContentRenderer } from '@/components/ContentRenderer';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useBookmarks } from '@/hooks/useBookmarks';
import { useThemeColor } from '@/hooks/useThemeColor';
import { BookmarkedQuestion } from '@/services/DataManager';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  FlatList,
  Modal,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { Text } from 'react-native-paper';

export default function BookmarksScreen() {
  const primaryColor = useThemeColor({}, 'primary');
  const surfaceColor = useThemeColor({}, 'surface');
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const shadowColor = useThemeColor({}, 'shadow');

  // Bookmark management
  const {
    bookmarks,
    loadingBookmarks,
    errorBookmarks,
    bookmarkSubjects,
    bookmarkBCSTitles,
    removeBookmark,
    searchBookmarks,
    getBookmarksBySubject,
    getBookmarksByBCS
  } = useBookmarks();

  // State for filtering and searching
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('All');
  const [selectedBCS, setSelectedBCS] = useState('All');
  const [filteredBookmarks, setFilteredBookmarks] = useState<BookmarkedQuestion[]>([]);
  const [showFilterModal, setShowFilterModal] = useState(false);

  // Filter bookmarks based on search and filters
  useEffect(() => {
    const filterBookmarks = async () => {
      console.log('🔍 Filtering bookmarks:', {
        totalBookmarks: bookmarks.length,
        selectedSubject,
        selectedBCS,
        searchQuery
      });

      let filtered = bookmarks;

      // Apply subject filter
      if (selectedSubject !== 'All') {
        filtered = await getBookmarksBySubject(selectedSubject);
      }

      // Apply BCS filter
      if (selectedBCS !== 'All') {
        filtered = await getBookmarksByBCS(selectedBCS);
      }

      // Apply search filter
      if (searchQuery.trim()) {
        filtered = await searchBookmarks(searchQuery);
      }

      console.log('📋 Filtered bookmarks result:', filtered.length);
      setFilteredBookmarks(filtered);
    };

    filterBookmarks();
  }, [bookmarks, selectedSubject, selectedBCS, searchQuery, getBookmarksBySubject, getBookmarksByBCS, searchBookmarks]);

  const handleQuestionPress = (bookmark: BookmarkedQuestion) => {
    router.push({
      pathname: '/questions',
      params: {
        title: bookmark.bcsTitle,
        fileUrl: bookmark.fileUrl
      }
    });
  };

  const handleRemoveBookmark = async (bookmark: BookmarkedQuestion) => {
    Alert.alert(
      'Remove Bookmark',
      'Are you sure you want to remove this bookmark?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeBookmark(bookmark, bookmark.fileUrl);
            } catch (error) {
              Alert.alert('Error', 'Failed to remove bookmark');
            }
          }
        }
      ]
    );
  };

  const renderBookmarkItem = ({ item }: { item: BookmarkedQuestion }) => (
    <TouchableOpacity
      style={[styles.bookmarkCard, { backgroundColor: surfaceColor, borderColor, shadowColor }]}
      onPress={() => handleQuestionPress(item)}
    >
      <ThemedView style={styles.bookmarkContent}>
        <ThemedView style={styles.bookmarkHeader}>
          <ThemedText style={[styles.questionNumber, { color: primaryColor }]}>
            Q{item.question_number}
          </ThemedText>
          {/* <ThemedText style={styles.bcsTitle}>{item.bcsTitle}</ThemedText>
           */}

           <ContentRenderer
            content={item.bcsTitle}
            style={styles.bcsTitle}
          />
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => handleRemoveBookmark(item)}
          >
            <IconSymbol name="xmark" size={16} color="#666" />
          </TouchableOpacity>
        </ThemedView>

        <ThemedView style={styles.questionContent}>
          <ContentRenderer
            content={item.question_text}
            style={styles.questionText}
          />
        </ThemedView>

        <ThemedView style={styles.bookmarkFooter}>
          <ThemedText style={styles.subjectTag}>{item.subject}</ThemedText>
          <ThemedText style={styles.bookmarkDate}>
            {new Date(item.bookmarkedAt).toLocaleDateString()}
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </TouchableOpacity>
  );

  if (loadingBookmarks) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={[styles.header, { backgroundColor: primaryColor }]}>
          <ThemedView style={styles.headerContent}>
            <ThemedText type="title" style={styles.headerTitle}>
              Bookmarks
            </ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedView style={styles.loadingContainer}>
          <ThemedText>Loading bookmarks...</ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  if (errorBookmarks) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={[styles.header, { backgroundColor: primaryColor }]}>
          <ThemedView style={styles.headerContent}>
            <ThemedText type="title" style={styles.headerTitle}>
              Bookmarks
            </ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedView style={styles.loadingContainer}>
          <ThemedText type="title">Error Loading Bookmarks</ThemedText>
          <ThemedText style={{ marginTop: 10, textAlign: 'center' }}>
            {errorBookmarks}
          </ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <ThemedView style={[styles.header, { backgroundColor: primaryColor, shadowColor }]}>
        <ThemedView style={styles.headerContent}>
          <ThemedText type="title" style={styles.headerTitle}>
            Bookmarks ({filteredBookmarks.length})
          </ThemedText>
          <ThemedText type="caption" style={styles.headerSubtitle}>
            Your saved questions and favorites
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {bookmarks.length === 0 ? (
        // Empty state
        <ThemedView style={styles.content}>
          <ThemedView style={styles.emptyState}>
            <ThemedText type="heading" style={styles.emptyTitle}>📖 No Bookmarks Yet</ThemedText>
            <ThemedText type="secondary" style={styles.emptyDescription}>
              Start practicing questions and bookmark your favorites to see them here.
            </ThemedText>
          </ThemedView>
        </ThemedView>
      ) : (
        // Bookmarks list
        <ThemedView style={styles.content}>
          {/* Search and Filter Bar */}
          <ThemedView style={[styles.searchContainer, { backgroundColor: surfaceColor, borderColor }]}>
            <ThemedView style={styles.searchInputContainer}>
              <IconSymbol name="magnifyingglass" size={20} color={borderColor} style={styles.searchIcon} />
              <TextInput
                style={[styles.searchInput, { color: primaryColor }]}
                placeholder="Search bookmarks..."
                placeholderTextColor={borderColor}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </ThemedView>
            <TouchableOpacity
              style={[styles.filterButton, { backgroundColor: primaryColor }]}
              onPress={() => setShowFilterModal(true)}
            >
              <IconSymbol name="line.3.horizontal.decrease" size={20} color="white" />
            </TouchableOpacity>
          </ThemedView>

          {/* Bookmarks List */}
          <FlatList
            data={filteredBookmarks}
            renderItem={renderBookmarkItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
            ListEmptyComponent={
              <ThemedView style={styles.emptyState}>
                <ThemedText type="heading" style={styles.emptyTitle}>🔍 No Results Found</ThemedText>
                <ThemedText type="secondary" style={styles.emptyDescription}>
                  Try adjusting your search or filter criteria.
                </ThemedText>
              </ThemedView>
            }
          />
        </ThemedView>
      )}

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: surfaceColor }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: primaryColor }]}>Filter Bookmarks</Text>
              <TouchableOpacity onPress={() => setShowFilterModal(false)}>
                <IconSymbol name="xmark" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.filterOptions}>
              {/* Subject Filter */}
              <Text style={[styles.filterSectionTitle, { color: primaryColor }]}>Subject</Text>
              {bookmarkSubjects.map((subject) => (
                <TouchableOpacity
                  key={subject}
                  style={[
                    styles.filterOption,
                    selectedSubject === subject && { backgroundColor: primaryColor + '15' }
                  ]}
                  onPress={() => {
                    setSelectedSubject(subject);
                    setShowFilterModal(false);
                  }}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedSubject === subject && { color: primaryColor, fontWeight: '600' }
                  ]}>
                    {subject}
                  </Text>
                  {selectedSubject === subject && (
                    <Text style={[styles.checkmark, { color: primaryColor }]}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}

              {/* BCS Filter */}
              <Text style={[styles.filterSectionTitle, { color: primaryColor }]}>BCS Exam</Text>
              {bookmarkBCSTitles.map((bcsTitle) => (
                <TouchableOpacity
                  key={bcsTitle}
                  style={[
                    styles.filterOption,
                    selectedBCS === bcsTitle && { backgroundColor: primaryColor + '15' }
                  ]}
                  onPress={() => {
                    setSelectedBCS(bcsTitle);
                    setShowFilterModal(false);
                  }}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedBCS === bcsTitle && { color: primaryColor, fontWeight: '600' }
                  ]}>
                    {bcsTitle}
                  </Text>
                  {selectedBCS === bcsTitle && (
                    <Text style={[styles.checkmark, { color: primaryColor }]}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 20 : 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  headerContent: {
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  headerTitle: {
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  emptyDescription: {
    textAlign: 'center',
    lineHeight: 22,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  filterButton: {
    padding: 8,
    borderRadius: 8,
    marginLeft: 8,
  },
  filterButtonText: {
    color: 'white',
    fontSize: 16,
  },
  listContainer: {
    paddingBottom: 20,
  },
  bookmarkCard: {
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bookmarkContent: {
    padding: 16,
  },
  bookmarkHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  questionNumber: {
    fontSize: 14,
    fontWeight: '700',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginRight: 8,
  },
  bcsTitle: {
    flex: 1,
    fontSize: 12,
    fontWeight: '500',
    opacity: 0.7,
  },
  removeButton: {
    padding: 4,
  },
  removeButtonText: {
    fontSize: 16,
    color: '#ff4444',
  },
  questionContent: {
    marginBottom: 12,
  },
  questionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  bookmarkFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subjectTag: {
    fontSize: 12,
    fontWeight: '600',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  bookmarkDate: {
    fontSize: 12,
    opacity: 0.6,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    fontSize: 20,
    color: '#6b7280',
    fontWeight: '600',
  },
  filterOptions: {
    maxHeight: 400,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    padding: 16,
    paddingBottom: 8,
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  filterOptionText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
  },
  checkmark: {
    fontSize: 16,
    fontWeight: '600',
  },
});
