import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useOptimizedTheme } from '@/hooks/useOptimizedTheme';
import React from 'react';
import { Alert, Platform, StatusBar, StyleSheet, TouchableOpacity } from 'react-native';

export default function ProfileScreen() {
  // Use optimized theme for comfortable reading experience
  const colors = useOptimizedTheme();
  const { themeMode, toggleTheme } = require('@/contexts/ThemeContext').useTheme();

  const getThemeDisplayText = () => {
    switch (themeMode) {
      case 'light': return '☀️ Reading Mode (Light)';
      case 'dark': return '🌙 Night Reading Mode';
      case 'system': return '🔄 Auto (System)';
      default: return '🔄 Auto (System)';
    }
  };
  const handleDarkModePress = () => {
    toggleTheme();
  };

  const handleAboutPress = () => {
    Alert.alert(
      'About BCS Question Bank',
      'BCS Question Bank v1.0.0\n\n📚 Eye-Comfortable Reading Experience\nA comprehensive offline-first app for BCS exam preparation with comfortable reading themes.\n\n✨ Features:\n• 46+ BCS exam sets\n• 5000+ practice questions\n• Comfortable reading modes\n• Offline support\n• Free forever\n\n🎨 Reading Comfort:\n• Warm, sepia-toned light mode\n• Gentle dark mode for night reading\n• Reduced eye strain colors\n• Optimized typography\n\nDeveloped with ❤️ for BCS aspirants',
      [{ text: 'OK' }]
    );
  };

  return (
     <ThemedView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header with comfortable colors */}
      <ThemedView style={[styles.header, { backgroundColor: colors.primary, shadowColor: colors.shadow }]}>
        <ThemedView style={styles.headerContent} lightColor="transparent" darkColor="transparent">
          <ThemedText type="title" style={[styles.headerTitle, { color: colors.textInverse || '#F5F1EB' }]}>
            📚 Profile & Settings
          </ThemedText>
          <ThemedText type="caption" style={[styles.headerSubtitle, { color: colors.textInverse || '#F5F1EB' }]}>
            Comfortable reading experience settings
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Content with warm, comfortable styling */}
      <ThemedView style={[styles.content, { backgroundColor: colors.background }]}>
        {/* Profile Section */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle" style={[styles.sectionTitle, { color: colors.text }]}>👤 Account</ThemedText>
          <ThemedView style={[styles.profileCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <ThemedView style={[styles.profileInfo, { backgroundColor: 'transparent' }]}>
              <ThemedView style={[styles.avatar, { backgroundColor: colors.backgroundSecondary }]}>
                <ThemedText style={[styles.avatarText, { color: colors.primary }]}>�</ThemedText>
              </ThemedView>
              <ThemedView style={[styles.userInfo, { backgroundColor: 'transparent' }]}>
                <ThemedText type="defaultSemiBold" style={[styles.userName, { color: colors.text }]}>BCS Reader</ThemedText>
                <ThemedText type="secondary" style={[styles.userEmail, { color: colors.textSecondary }]}>Comfortable reading experience enabled</ThemedText>
              </ThemedView>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        {/* Settings Section */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle" style={[styles.sectionTitle, { color: colors.text }]}>⚙️ Reading Settings</ThemedText>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.surface, borderColor: colors.border }]}
            activeOpacity={0.7}
            onPress={handleDarkModePress}
          >
            <ThemedText style={[styles.settingIcon, { color: colors.primary }]}>�</ThemedText>
            <ThemedView style={styles.settingContent}>
              <ThemedText type="defaultSemiBold" style={{ color: colors.text }}>Reading Theme</ThemedText>
              <ThemedText type="secondary" style={{ color: colors.textSecondary }}>{getThemeDisplayText()}</ThemedText>
            </ThemedView>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: colors.surface, borderColor: colors.border }]}
            activeOpacity={0.7}
            onPress={handleAboutPress}
          >
            <ThemedText style={[styles.settingIcon, { color: colors.primary }]}>ℹ️</ThemedText>
            <ThemedView style={styles.settingContent}>
              <ThemedText type="defaultSemiBold" style={{ color: colors.text }}>About</ThemedText>
              <ThemedText type="secondary" style={{ color: colors.textSecondary }}>App info & reading features</ThemedText>
            </ThemedView>
          </TouchableOpacity>

          {/* Reading Comfort Info */}
          <ThemedView style={[styles.comfortInfo, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <ThemedText style={[styles.comfortTitle, { color: colors.primary }]}>👁️ Eye Comfort Features</ThemedText>
            <ThemedText style={[styles.comfortText, { color: colors.textSecondary }]}>
              • Warm, sepia-toned colors reduce blue light{'\n'}
              • Optimized contrast for comfortable reading{'\n'}
              • Gentle shadows and borders{'\n'}
              • Typography designed for long reading sessions
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 20 : 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  profileCard: {
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 24,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  settingIcon: {
    fontSize: 20,
    marginRight: 16,
    width: 24,
    textAlign: 'center',
  },
  settingContent: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  settingArrow: {
    fontSize: 18,
    opacity: 0.5,
  },
  // New comfortable reading styles
  comfortInfo: {
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  comfortTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  comfortText: {
    fontSize: 14,
    lineHeight: 20,
  },
});
