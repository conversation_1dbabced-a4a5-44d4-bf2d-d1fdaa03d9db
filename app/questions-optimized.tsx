import { ContentRenderer } from '@/components/ContentRenderer';
import { ModernLoader } from '@/components/ModernLoader';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useBookmarks } from '@/hooks/useBookmarks';
import { useDataManager } from '@/hooks/useDataManager';
import { useOptimizedTheme, useOptimizedStyles } from '@/hooks/useOptimizedTheme';
import { useProgress } from '@/hooks/useProgress';
import { router, useLocalSearchParams } from 'expo-router';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  Alert,
  Animated,
  Modal,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';
import { Button, Text } from 'react-native-paper';

// ===== PERFORMANCE OPTIMIZATIONS =====

/**
 * 1. MEMOIZED COMPONENTS - Prevent unnecessary re-renders
 * These components only re-render when their specific props change
 */

// Memoized Question Header Component
const QuestionHeader = memo(({
  currentQuestion,
  currentQuestionIndex,
  filteredQuestionsLength,
  selectedSubject,
  primaryColor
}: {
  currentQuestion: any;
  currentQuestionIndex: number;
  filteredQuestionsLength: number;
  selectedSubject: string;
  primaryColor: string;
}) => (
  <ThemedView style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
    <ThemedView style={{ flexDirection: 'row', alignItems: 'center' }}>
      <ThemedText type="subheading" style={[styles.questionNumber, { color: primaryColor }]}>
        Question {currentQuestion.question_number}
      </ThemedText>
    </ThemedView>
    <ThemedView style={{ alignItems: 'flex-end' }}>
      <ThemedText style={[styles.questionNumber, { color: 'gray' }]}>
        {currentQuestionIndex + 1}/{filteredQuestionsLength}
        {selectedSubject !== 'All' && ` • ${selectedSubject}`}
      </ThemedText>
    </ThemedView>
  </ThemedView>
));
QuestionHeader.displayName = 'QuestionHeader';

// Memoized Option Component with optimized style calculations
const OptionItem = memo(({
  optionKey,
  optionValue,
  selectedAnswer,
  showAnswer,
  correctAnswer,
  onPress,
  colors
}: {
  optionKey: string;
  optionValue: string;
  selectedAnswer: string;
  showAnswer: boolean;
  correctAnswer: string;
  onPress: (key: string) => void;
  colors: any;
}) => {
  // Memoized state calculations
  const isSelected = selectedAnswer === optionKey;
  const isCorrect = showAnswer && optionKey === correctAnswer;
  const isWrong = showAnswer && selectedAnswer === optionKey && optionKey !== correctAnswer;

  // Memoized styles to prevent recreation on every render
  const optionStyle = useMemo(() => [
    styles.optionItem,
    { backgroundColor: colors.surface, borderColor: colors.border, shadowColor: colors.border },
    isSelected && { backgroundColor: colors.primary + '15', borderColor: colors.primary, shadowColor: colors.primary },
    isCorrect && { backgroundColor: colors.success + '15', borderColor: colors.success, shadowColor: colors.success },
    isWrong && { backgroundColor: colors.error + '15', borderColor: colors.error, shadowColor: colors.error }
  ], [isSelected, isCorrect, isWrong, colors]);

  const labelStyle = useMemo(() => [
    styles.optionLabel,
    {
      color: isSelected ? 'white' : colors.primary,
      backgroundColor: isSelected ? colors.primary : colors.surface,
      borderColor: colors.primary,
      borderWidth: isSelected ? 0 : 1
    }
  ], [isSelected, colors]);

  // Memoized handler to prevent function recreation
  const handlePress = useCallback(() => {
    onPress(optionKey);
  }, [optionKey, onPress]);

  return (
    <TouchableOpacity style={optionStyle} onPress={handlePress} disabled={showAnswer}>
      <ThemedView style={styles.optionTextContainer}>
        <ThemedText style={labelStyle}>{optionKey}</ThemedText>
        <ContentRenderer content={optionValue} style={styles.optionText} />
      </ThemedView>
    </TouchableOpacity>
  );
});
OptionItem.displayName = 'OptionItem';

/**
 * 2. MAIN COMPONENT WITH OPTIMIZATIONS
 */
export default function OptimizedQuestionsScreen() {
  const params = useLocalSearchParams();
  const { title, fileUrl } = params;
  
  // OPTIMIZATION 1: Single theme hook call instead of 7 individual calls
  const colors = useOptimizedTheme();
  const optimizedStyles = useOptimizedStyles(colors);

  // Data hooks
  const { loadQuestions, questions, loadingQuestions, errorQuestions } = useDataManager();
  const { toggleBookmark, isBookmarked } = useBookmarks();
  const { saveProgress, getProgress, markQuestionCompleted, clearProgress } = useProgress();

  // State
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState('');
  const [showAnswer, setShowAnswer] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);
  const [completedQuestions, setCompletedQuestions] = useState<number[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState<string>('All');
  const [showFilterModal, setShowFilterModal] = useState(false);

  // Animation values
  const slideAnim = useState(new Animated.Value(0))[0];
  const fadeAnim = useState(new Animated.Value(1))[0];

  // Ad hook
  const { useInterstitialAd } = require('@/components/ads');
  const { showAd } = useInterstitialAd();

  // OPTIMIZATION 2: Memoized computed values
  const availableSubjects = useMemo(() => {
    if (!questions || questions.length === 0) return ['All'];
    const subjects = [...new Set(questions.map(q => q.subject))].filter(Boolean);
    return ['All', ...subjects.sort()];
  }, [questions]);

  const filteredQuestions = useMemo(() => {
    if (!questions) return [];
    if (selectedSubject === 'All') return questions;
    return questions.filter(q => q.subject === selectedSubject);
  }, [questions, selectedSubject]);

  const currentQuestion = filteredQuestions[currentQuestionIndex];

  // OPTIMIZATION 3: Memoized event handlers to prevent child re-renders
  const handleAnswerSelect = useCallback(async (answer: string) => {
    if (showAnswer) return;
    setSelectedAnswer(answer);
    setShowAnswer(true);

    if (!completedQuestions.includes(currentQuestionIndex)) {
      const newCompletedQuestions = [...completedQuestions, currentQuestionIndex];
      setCompletedQuestions(newCompletedQuestions);

      if (fileUrl) {
        try {
          await markQuestionCompleted(fileUrl as string, currentQuestionIndex);
        } catch (error) {
          console.error('Error marking question as completed:', error);
        }
      }
    }
  }, [showAnswer, completedQuestions, currentQuestionIndex, fileUrl, markQuestionCompleted]);

  const handleBookmarkToggle = useCallback(async () => {
    if (!currentQuestion) return;
    try {
      await toggleBookmark(currentQuestion, title as string, fileUrl as string);
    } catch (error) {
      console.error('Bookmark toggle failed:', error);
      Alert.alert('Error', 'Failed to update bookmark');
    }
  }, [currentQuestion, title, fileUrl, toggleBookmark]);

  // Effects
  useEffect(() => {
    if (fileUrl) {
      loadQuestions(fileUrl as string);
    }
  }, [fileUrl, loadQuestions]);

  useEffect(() => {
    const loadProgress = async () => {
      if (fileUrl && questions.length > 0) {
        try {
          const progress = await getProgress(fileUrl as string);
          if (progress) {
            setCurrentQuestionIndex(progress.currentQuestionIndex);
            setSelectedSubject(progress.selectedSubject);
            setCompletedQuestions(progress.completedQuestions);
          }
        } catch (error) {
          console.error('Error loading progress:', error);
        }
      }
    };
    loadProgress();
  }, [fileUrl, questions.length, getProgress]);

  // Loading states
  if (loadingQuestions) {
    return (
      <ModernLoader
        message="Loading Questions"
        submessage={`Preparing ${title} questions for you...`}
      />
    );
  }

  if (errorQuestions) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText type="title">Error Loading Questions</ThemedText>
        <ThemedText style={{ marginTop: 10, textAlign: 'center' }}>
          {errorQuestions}
        </ThemedText>
        <Button onPress={() => router.back()} style={{ marginTop: 20 }}>
          Go Back
        </Button>
      </ThemedView>
    );
  }

  if (questions.length === 0) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText type="title">No Questions Found</ThemedText>
        <Button onPress={() => router.back()}>Go Back</Button>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={[styles.container, optimizedStyles.container]}>
      {/* Header with optimized styles */}
      <ThemedView style={[styles.header, optimizedStyles.header]}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <IconSymbol name="arrow.left" size={24} color="white" />
        </TouchableOpacity>
        <ThemedView style={[styles.headerCenter, { backgroundColor: 'transparent' }]}>
          <ThemedText style={[styles.headerTitle, { color: 'white' }]}>
            {title}
          </ThemedText>
        </ThemedView>
        <ThemedView style={[styles.headerActions, { backgroundColor: 'transparent' }]}>
          <TouchableOpacity onPress={handleBookmarkToggle} style={styles.bookmarkButton}>
            <IconSymbol
              name={currentQuestion && isBookmarked(currentQuestion, fileUrl as string) ? 'bookmark.fill' : 'bookmark'}
              size={24}
              color="white"
            />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setShowFilterModal(true)}
            style={styles.filterButton}
          >
            <IconSymbol name="line.3.horizontal.decrease" size={24} color="white" />
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>

      <ScrollView style={[styles.content, optimizedStyles.container]}>
        <Animated.View
          style={[
            styles.questionCard,
            optimizedStyles.questionCard,
            {
              transform: [{ translateX: slideAnim }],
              opacity: fadeAnim
            }
          ]}
        >
          <ThemedView style={styles.cardContent}>
            {/* Using memoized header component */}
            <QuestionHeader
              currentQuestion={currentQuestion}
              currentQuestionIndex={currentQuestionIndex}
              filteredQuestionsLength={filteredQuestions.length}
              selectedSubject={selectedSubject}
              primaryColor={colors.primary}
            />
            
            <ThemedView style={styles.questionTextContainer}>
              <ContentRenderer
                content={currentQuestion.question_text}
                style={styles.questionText}
              />
            </ThemedView>

            <ThemedView style={styles.optionsContainer}>
              {/* Using memoized option components */}
              {Object.entries(currentQuestion.options).map(([key, value]) => (
                <OptionItem
                  key={key}
                  optionKey={key}
                  optionValue={value}
                  selectedAnswer={selectedAnswer}
                  showAnswer={showAnswer}
                  correctAnswer={currentQuestion.correct_answer}
                  onPress={handleAnswerSelect}
                  colors={colors}
                />
              ))}
            </ThemedView>

            {showAnswer && (
              <ThemedView style={[styles.answerContainer, optimizedStyles.answerContainer]}>
                {currentQuestion.explanation && !showExplanation && (
                  <ThemedView style={styles.showExplanationContainer}>
                    <Button
                      mode="outlined"
                      onPress={() => setShowExplanation(true)}
                      style={styles.showExplanationButton}
                    >
                      Show Explanation
                    </Button>
                  </ThemedView>
                )}

                {currentQuestion.explanation && showExplanation && (
                  <ThemedView style={[styles.explanationContainer, optimizedStyles.explanationContainer]}>
                    <ThemedText style={[styles.explanationLabel, { color: colors.success }]}>
                      Explanation:
                    </ThemedText>
                    <ContentRenderer
                      content={currentQuestion.explanation}
                      style={styles.explanationText}
                    />
                  </ThemedView>
                )}
              </ThemedView>
            )}
          </ThemedView>
        </Animated.View>
      </ScrollView>

      {/* Bottom Navigation with optimized styles */}
      <ThemedView style={[styles.bottomNavigation, optimizedStyles.bottomNavigation]}>
        <Button
          mode="outlined"
          onPress={() => {/* handlePreviousQuestion */}}
          disabled={currentQuestionIndex === 0}
          style={[styles.navButton, optimizedStyles.navButton]}
          labelStyle={{ color: colors.primary }}
        >
          Previous
        </Button>
        <Button
          mode="contained"
          onPress={() => {/* handleNextQuestion */}}
          style={[styles.navButton, optimizedStyles.navButtonContained]}
          labelStyle={{ color: 'white' }}
        >
          {currentQuestionIndex === filteredQuestions.length - 1 ? 'Finish' : 'Next'}
        </Button>
      </ThemedView>
    </ThemedView>
  );
}

// Styles remain the same as original
const styles = StyleSheet.create({
  container: { flex: 1 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
  header: {
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 20 : 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  backButton: { padding: 8, borderRadius: 8 },
  headerCenter: { flex: 1, alignItems: 'center', marginHorizontal: 16 },
  headerTitle: { fontSize: 18, fontWeight: '600', textAlign: 'center' },
  headerActions: { flexDirection: 'row', alignItems: 'center', gap: 8 },
  bookmarkButton: { padding: 8, borderRadius: 8 },
  filterButton: { padding: 8, borderRadius: 8 },
  content: { flex: 1, padding: 12 },
  questionCard: {
    borderRadius: 16,
    marginBottom: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  cardContent: { padding: 16 },
  questionNumber: {
    fontSize: 18,
    fontWeight: '800',
    marginBottom: 12,
    textAlign: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 10,
    alignSelf: 'flex-start',
  },
  questionTextContainer: { marginBottom: 16, padding: 2 },
  questionText: { fontSize: 18, lineHeight: 28, fontWeight: '500' },
  optionsContainer: { marginBottom: 16, gap: 8 },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 2,
    minHeight: 60,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 4,
    transform: [{ scale: 1 }],
  },
  optionTextContainer: { flex: 1, flexDirection: 'row', alignItems: 'center', marginLeft: 12 },
  optionLabel: {
    fontSize: 16,
    fontWeight: '800',
    minWidth: 28,
    height: 28,
    textAlign: 'center',
    lineHeight: 28,
    borderRadius: 14,
    marginRight: 10,
  },
  optionText: { flex: 1, fontSize: 17, lineHeight: 26, fontWeight: '500' },
  answerContainer: {
    padding: 14,
    borderRadius: 12,
    marginTop: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  showExplanationContainer: { marginTop: 12 },
  showExplanationButton: { borderRadius: 10 },
  explanationContainer: { marginTop: 12, padding: 16, borderRadius: 12 },
  explanationLabel: { fontSize: 16, fontWeight: '700', marginBottom: 8 },
  explanationText: { fontSize: 16, lineHeight: 24, fontWeight: '500' },
  bottomNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 8,
  },
  navButton: {
    flex: 0.45,
    borderRadius: 12,
    paddingVertical: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
});
