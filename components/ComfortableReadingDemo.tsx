import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { ThemedView } from './ThemedView';
import { ThemedText } from './ThemedText';
import { useOptimizedTheme } from '@/hooks/useOptimizedTheme';

/**
 * Demo component showing the comfortable reading theme in action
 * This demonstrates the eye-friendly colors and typography
 */
export const ComfortableReadingDemo = () => {
  const colors = useOptimizedTheme();

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={[styles.header, { backgroundColor: colors.primary }]}>
        <ThemedText style={[styles.headerTitle, { color: colors.textInverse }]}>
          👁️ Comfortable Reading Experience
        </ThemedText>
        <ThemedText style={[styles.headerSubtitle, { color: colors.textInverse }]}>
          Eye-friendly colors for better study sessions
        </ThemedText>
      </ThemedView>

      {/* Content Card */}
      <ThemedView style={[styles.card, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <ThemedText style={[styles.cardTitle, { color: colors.text }]}>
          📚 Sample BCS Question
        </ThemedText>
        
        <ThemedText style={[styles.questionText, { color: colors.text }]}>
          Which of the following is the capital of Bangladesh?
        </ThemedText>

        {/* Options */}
        <ThemedView style={styles.optionsContainer}>
          <ThemedView style={[styles.option, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]}>
            <ThemedText style={[styles.optionLabel, { color: colors.primary }]}>A</ThemedText>
            <ThemedText style={[styles.optionText, { color: colors.text }]}>Chittagong</ThemedText>
          </ThemedView>

          <ThemedView style={[styles.option, styles.selectedOption, { backgroundColor: colors.primary + '15', borderColor: colors.primary }]}>
            <ThemedText style={[styles.optionLabel, { color: colors.primary }]}>B</ThemedText>
            <ThemedText style={[styles.optionText, { color: colors.text }]}>Dhaka</ThemedText>
          </ThemedView>

          <ThemedView style={[styles.option, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]}>
            <ThemedText style={[styles.optionLabel, { color: colors.primary }]}>C</ThemedText>
            <ThemedText style={[styles.optionText, { color: colors.text }]}>Sylhet</ThemedText>
          </ThemedView>

          <ThemedView style={[styles.option, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]}>
            <ThemedText style={[styles.optionLabel, { color: colors.primary }]}>D</ThemedText>
            <ThemedText style={[styles.optionText, { color: colors.text }]}>Rajshahi</ThemedText>
          </ThemedView>
        </ThemedView>

        {/* Explanation */}
        <ThemedView style={[styles.explanation, { backgroundColor: colors.success + '10', borderColor: colors.success + '30' }]}>
          <ThemedText style={[styles.explanationTitle, { color: colors.success }]}>
            ✓ Correct Answer
          </ThemedText>
          <ThemedText style={[styles.explanationText, { color: colors.textSecondary }]}>
            Dhaka is the capital and largest city of Bangladesh. It's located in the central part of the country on the Buriganga River.
          </ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Features Card */}
      <ThemedView style={[styles.card, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <ThemedText style={[styles.cardTitle, { color: colors.text }]}>
          🎨 Eye Comfort Features
        </ThemedText>

        <ThemedView style={styles.featuresList}>
          <ThemedView style={styles.feature}>
            <ThemedText style={[styles.featureIcon, { color: colors.primary }]}>🌅</ThemedText>
            <ThemedView style={styles.featureContent}>
              <ThemedText style={[styles.featureTitle, { color: colors.text }]}>Warm Color Palette</ThemedText>
              <ThemedText style={[styles.featureDescription, { color: colors.textSecondary }]}>
                Sepia-toned colors reduce blue light exposure
              </ThemedText>
            </ThemedView>
          </ThemedView>

          <ThemedView style={styles.feature}>
            <ThemedText style={[styles.featureIcon, { color: colors.primary }]}>📖</ThemedText>
            <ThemedView style={styles.featureContent}>
              <ThemedText style={[styles.featureTitle, { color: colors.text }]}>Paper-like Background</ThemedText>
              <ThemedText style={[styles.featureDescription, { color: colors.textSecondary }]}>
                Mimics traditional book reading experience
              </ThemedText>
            </ThemedView>
          </ThemedView>

          <ThemedView style={styles.feature}>
            <ThemedText style={[styles.featureIcon, { color: colors.primary }]}>👁️</ThemedText>
            <ThemedView style={styles.featureContent}>
              <ThemedText style={[styles.featureTitle, { color: colors.text }]}>Reduced Eye Strain</ThemedText>
              <ThemedText style={[styles.featureDescription, { color: colors.textSecondary }]}>
                Optimized contrast for comfortable long reading
              </ThemedText>
            </ThemedView>
          </ThemedView>

          <ThemedView style={styles.feature}>
            <ThemedText style={[styles.featureIcon, { color: colors.primary }]}>🌙</ThemedText>
            <ThemedView style={styles.featureContent}>
              <ThemedText style={[styles.featureTitle, { color: colors.text }]}>Night Reading Mode</ThemedText>
              <ThemedText style={[styles.featureDescription, { color: colors.textSecondary }]}>
                Warm dark theme for comfortable night study
              </ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    padding: 24,
    borderRadius: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.9,
  },
  card: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  questionText: {
    fontSize: 18,
    lineHeight: 26,
    marginBottom: 20,
    fontWeight: '500',
  },
  optionsContainer: {
    marginBottom: 20,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 2,
  },
  selectedOption: {
    // Additional styling for selected option
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 12,
    minWidth: 24,
    textAlign: 'center',
  },
  optionText: {
    fontSize: 16,
    flex: 1,
    fontWeight: '500',
  },
  explanation: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  explanationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  explanationText: {
    fontSize: 15,
    lineHeight: 22,
  },
  featuresList: {
    gap: 16,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  featureIcon: {
    fontSize: 24,
    marginRight: 16,
    marginTop: 2,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});
