import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

interface BannerAdComponentProps {
  size?: any;
  style?: any;
}

export const BannerAdComponent: React.FC<BannerAdComponentProps> = ({
  size,
  style
}) => {
  try {
    const { BannerAd, BannerAdSize, TestIds } = require('react-native-google-mobile-ads');

    if (!BannerAd) {
      return (
        <View style={[styles.container, style]}>
          <Text style={styles.errorText}>Banner Ad not available</Text>
        </View>
      );
    }

    // Use test ad unit ID for development, replace with your actual ad unit ID for production
    const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-4790670410909128/6213280472';

    return (
      <View style={[styles.container, style]}>
        <BannerAd
          unitId={adUnitId}
          size={size || BannerAdSize.BANNER}
          requestOptions={{
            requestNonPersonalizedAdsOnly: true,
          }}
          onAdLoaded={() => {
            console.log('Banner ad loaded');
          }}
          onAdFailedToLoad={(error:any) => {
            console.log('Banner ad failed to load:', error);
          }}
        />
      </View>
    );
  } catch (error) {
    console.error('BannerAd error:', error);
    return (
      <View style={[styles.container, style]}>
        <Text style={styles.errorText}>Ad loading failed</Text>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  errorText: {
    color: '#666',
    fontSize: 12,
    textAlign: 'center',
  },
});
