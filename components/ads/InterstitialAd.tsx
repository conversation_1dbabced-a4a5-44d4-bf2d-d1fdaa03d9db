import { useEffect, useState } from 'react';
import { Platform, StatusBar } from 'react-native';

export const useInterstitialAd = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [interstitial, setInterstitial] = useState<any>(null);

  useEffect(() => {
    try {
      const { AdEventType, InterstitialAd, TestIds } = require('react-native-google-mobile-ads');

      if (!InterstitialAd) {
        console.error('InterstitialAd not available');
        return;
      }

      // Use test ad unit ID for development, replace with your actual ad unit ID for production
      const adUnitId = __DEV__ ? TestIds.INTERSTITIAL : 'ca-app-pub-4790670410909128/7077701782';

      const interstitialAd = InterstitialAd.createForAdRequest(adUnitId, {
        requestNonPersonalizedAdsOnly: true,
      });

      setInterstitial(interstitialAd);

      const unsubscribeLoaded = interstitialAd.addAdEventListener(AdEventType.LOADED, () => {
        setIsLoaded(true);
        setIsLoading(false);
        console.log('✅ Interstitial ad loaded successfully');
      });

      const unsubscribeError = interstitialAd.addAdEventListener(AdEventType.ERROR, (error: any) => {
        setIsLoading(false);
        setIsLoaded(false);
        console.error('❌ Interstitial ad error:', error);
        // Retry loading after error
        setTimeout(() => {
          loadAd();
        }, 5000);
      });

      const unsubscribeOpened = interstitialAd.addAdEventListener(AdEventType.OPENED, () => {
        console.log('📱 Interstitial ad opened');
        if (Platform.OS === 'ios') {
          StatusBar.setHidden(true);
        }
      });

      const unsubscribeClosed = interstitialAd.addAdEventListener(AdEventType.CLOSED, () => {
        console.log('🔄 Interstitial ad closed - loading next ad');
        if (Platform.OS === 'ios') {
          StatusBar.setHidden(false);
        }
        setIsLoaded(false);
        // Load a new ad for next time
        loadAd();
      });

      const loadAd = () => {
        if (!isLoading) {
          setIsLoading(true);
          console.log('🔄 Loading interstitial ad...');
          interstitialAd.load();
        }
      };

      // Load the initial ad
      loadAd();

      return () => {
        unsubscribeLoaded();
        unsubscribeError();
        unsubscribeOpened();
        unsubscribeClosed();
      };
    } catch (error) {
      console.error('Failed to initialize InterstitialAd:', error);
    }
  }, []);

  const loadAd = () => {
    if (interstitial && !isLoading && !isLoaded) {
      setIsLoading(true);
      console.log('🔄 Loading interstitial ad...');
      interstitial.load();
    }
  };

  const showAd = () => {
    if (interstitial && isLoaded) {
      console.log('📺 Showing interstitial ad');
      interstitial.show().catch((error: any) => {
        console.error('❌ Failed to show interstitial ad:', error);
        setIsLoaded(false);
        // Try to load a new ad after failure
        setTimeout(() => {
          loadAd();
        }, 2000);
      });
    } else {
      console.log('⚠️ Interstitial ad not ready yet, loading...');
      loadAd();
    }
  };

  return {
    isLoaded,
    isLoading,
    showAd,
    loadAd,
  };
};
