import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View, Image, Animated } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

interface NativeAdCardProps {
  style?: any;
  onAdLoaded?: () => void;
  onAdFailedToLoad?: (error: any) => void;
}

export const NativeAdCard: React.FC<NativeAdCardProps> = ({
  style,
  onAdLoaded,
  onAdFailedToLoad
}) => {
  const [nativeAd, setNativeAd] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get theme colors to match your question cards
  const primaryColor = useThemeColor({}, 'primary');
  const surfaceColor = useThemeColor({}, 'surface');
  const shadowColor = useThemeColor({}, 'shadow');
  const borderColor = useThemeColor({}, 'border');

  useEffect(() => {
    loadNativeAd();
    
    // Cleanup function
    return () => {
      if (nativeAd) {
        nativeAd.destroy();
      }
    };
  }, []);

  const loadNativeAd = async () => {
    try {
      const { NativeAd, TestIds } = require('react-native-google-mobile-ads');

      if (!NativeAd) {
        throw new Error('NativeAd not available');
      }

      setIsLoading(true);
      setError(null);

      // Use test ad unit ID for development, replace with your actual ad unit ID for production
      const adUnitId = __DEV__ ? TestIds.NATIVE : 'ca-app-pub-****************/YOUR_NATIVE_AD_UNIT_ID';

      const ad = await NativeAd.createForAdRequest(adUnitId, {
        requestNonPersonalizedAdsOnly: true,
      });

      setNativeAd(ad);
      setIsLoading(false);
      onAdLoaded?.();
      console.log('Native ad loaded successfully');

    } catch (error) {
      console.error('Native ad failed to load:', error);
      setError('Failed to load ad');
      setIsLoading(false);
      onAdFailedToLoad?.(error);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer, { backgroundColor: surfaceColor, shadowColor }, style]}>
        <Text style={[styles.loadingText, { color: primaryColor }]}>Loading ad...</Text>
      </View>
    );
  }

  // Show error state
  if (error || !nativeAd) {
    return (
      <View style={[styles.container, styles.errorContainer, { backgroundColor: surfaceColor, shadowColor }, style]}>
        <Text style={styles.errorText}>Ad not available</Text>
      </View>
    );
  }

  // Render the native ad
  return (
    <NativeAdContent 
      nativeAd={nativeAd} 
      style={style}
      primaryColor={primaryColor}
      surfaceColor={surfaceColor}
      shadowColor={shadowColor}
      borderColor={borderColor}
    />
  );
};

// Separate component for rendering the actual native ad content
const NativeAdContent: React.FC<{
  nativeAd: any;
  style?: any;
  primaryColor: string;
  surfaceColor: string;
  shadowColor: string;
  borderColor: string;
}> = ({ nativeAd, style, primaryColor, surfaceColor, shadowColor, borderColor }) => {
  try {
    const { NativeAdView, NativeAsset, NativeMediaView, NativeAssetType } = require('react-native-google-mobile-ads');

    return (
      <NativeAdView nativeAd={nativeAd} style={[styles.container, { backgroundColor: surfaceColor, shadowColor }, style]}>
        <View style={styles.cardContent}>
          {/* Ad Attribution - Required for programmatic native ads */}
          <View style={[styles.sponsoredContainer, { borderColor: primaryColor }]}>
            <Text style={[styles.sponsoredText, { color: primaryColor }]}>Sponsored</Text>
          </View>

          {/* Header with Icon and Headline */}
          <View style={styles.adHeader}>
            {nativeAd.icon && (
              <NativeAsset assetType={NativeAssetType.ICON}>
                <Image 
                  source={{ uri: nativeAd.icon.url }} 
                  style={styles.adIcon}
                />
              </NativeAsset>
            )}
            
            <View style={styles.adHeaderText}>
              <NativeAsset assetType={NativeAssetType.HEADLINE}>
                <Text style={[styles.adHeadline, { color: primaryColor }]} numberOfLines={2}>
                  {nativeAd.headline}
                </Text>
              </NativeAsset>
              
              {nativeAd.advertiser && (
                <NativeAsset assetType={NativeAssetType.ADVERTISER}>
                  <Text style={styles.adAdvertiser} numberOfLines={1}>
                    {nativeAd.advertiser}
                  </Text>
                </NativeAsset>
              )}
            </View>
          </View>

          {/* Body Text */}
          {nativeAd.body && (
            <NativeAsset assetType={NativeAssetType.BODY}>
              <Text style={styles.adBody} numberOfLines={3}>
                {nativeAd.body}
              </Text>
            </NativeAsset>
          )}

          {/* Media Content */}
          <View style={styles.mediaContainer}>
            <NativeMediaView 
              style={styles.adMedia}
              resizeMode="cover"
            />
          </View>

          {/* Call to Action Button */}
          {nativeAd.callToAction && (
            <NativeAsset assetType={NativeAssetType.CALL_TO_ACTION}>
              <View style={[styles.ctaButton, { backgroundColor: primaryColor }]}>
                <Text style={styles.ctaText}>
                  {nativeAd.callToAction}
                </Text>
              </View>
            </NativeAsset>
          )}
        </View>
      </NativeAdView>
    );
  } catch (error) {
    console.error('Error rendering native ad content:', error);
    return (
      <View style={[styles.container, styles.errorContainer, { backgroundColor: surfaceColor, shadowColor }, style]}>
        <Text style={styles.errorText}>Ad rendering failed</Text>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginBottom: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  errorText: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
  },
  cardContent: {
    padding: 16,
  },
  sponsoredContainer: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    borderWidth: 1,
    marginBottom: 12,
  },
  sponsoredText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  adHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  adIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 12,
  },
  adHeaderText: {
    flex: 1,
  },
  adHeadline: {
    fontSize: 18,
    fontWeight: '700',
    lineHeight: 22,
    marginBottom: 4,
  },
  adAdvertiser: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  adBody: {
    fontSize: 16,
    lineHeight: 20,
    color: '#333',
    marginBottom: 12,
  },
  mediaContainer: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  adMedia: {
    width: '100%',
    height: 200,
    borderRadius: 12,
  },
  ctaButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  ctaText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
