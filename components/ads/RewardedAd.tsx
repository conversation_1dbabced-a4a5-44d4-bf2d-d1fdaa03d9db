// import { useEffect, useState } from 'react';
// import { RewardedAd, RewardedAdEventType, TestIds } from 'react-native-google-mobile-ads';

// // Use test ad unit ID for development, replace with your actual ad unit ID for production
// const adUnitId = __DEV__ ? TestIds.REWARDED : 'ca-app-pub-4790670410909128/3828721913';

// const rewarded = RewardedAd.createForAdUnitId(adUnitId, {
//   requestNonPersonalizedAdsOnly: true,
// });

// export const useRewardedAd = () => {
//   const [isLoaded, setIsLoaded] = useState(false);
//   const [isLoading, setIsLoading] = useState(false);
//   const [reward, setReward] = useState<any>(null);

//   useEffect(() => {
//     const unsubscribeLoaded = rewarded.addAdEventListener(RewardedAdEventType.LOADED, () => {
//       setIsLoaded(true);
//       setIsLoading(false);
//       console.log('Rewarded ad loaded');
//     });

//     const unsubscribeError = rewarded.addAdEventListener(RewardedAdEventType.ERROR, (error) => {
//       setIsLoading(false);
//       console.log('Rewarded ad error:', error);
//     });

//     const unsubscribeEarned = rewarded.addAdEventListener(
//       RewardedAdEventType.EARNED_REWARD,
//       (reward) => {
//         setReward(reward);
//         console.log('User earned reward:', reward);
//       }
//     );

//     const unsubscribeClosed = rewarded.addAdEventListener(RewardedAdEventType.CLOSED, () => {
//       setIsLoaded(false);
//       console.log('Rewarded ad closed');
//       // Load a new ad for next time
//       loadAd();
//     });

//     // Load the initial ad
//     loadAd();

//     return () => {
//       unsubscribeLoaded();
//       unsubscribeError();
//       unsubscribeEarned();
//       unsubscribeClosed();
//     };
//   }, []);

//   const loadAd = () => {
//     if (!isLoading && !isLoaded) {
//       setIsLoading(true);
//       rewarded.load();
//     }
//   };

//   const showAd = () => {
//     if (isLoaded) {
//       rewarded.show();
//     } else {
//       console.log('Rewarded ad not ready yet');
//       // Optionally load a new ad
//       loadAd();
//     }
//   };

//   return {
//     isLoaded,
//     isLoading,
//     reward,
//     showAd,
//     loadAd,
//     clearReward: () => setReward(null),
//   };
// };
