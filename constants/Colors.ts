/**
 * Eye-comfortable reading mode color palette for BCS Question Bank app.
 * Colors are specifically designed for reduced eye strain, optimal readability,
 * and comfortable long reading sessions in both light and dark modes.
 *
 * Design principles:
 * - Warm, soft colors to reduce blue light exposure
 * - High contrast ratios for better readability
 * - Muted tones to prevent eye fatigue
 * - Sepia-inspired light mode for paper-like reading experience
 */

// Comfortable primary colors (warm, muted tones)
const primaryLight = '#8B7355';  // Warm brown - easy on eyes
const primaryDark = '#D4B896';   // Soft cream - gentle contrast

// Secondary accent colors (earth tones)
const secondaryLight = '#A0845C'; // Muted gold
const secondaryDark = '#E6C79C';  // Light gold

// Success/Error colors (softer, less aggressive)
const successLight = '#6B8E5A';  // Muted green
const successDark = '#9BB88A';   // Soft green
const errorLight = '#B85C5C';    // Muted red
const errorDark = '#D49999';     // Soft red

export const Colors = {
  light: {
    // Text colors - warm, high contrast for comfortable reading
    text: '#3C2E26',           // Dark warm brown - easy to read
    textSecondary: '#6B5B4F',  // Medium brown - for secondary text
    textMuted: '#8B7D71',      // Light brown - for muted text
    textInverse: '#F5F1EB',    // Warm white - for dark backgrounds

    // Background colors - sepia/paper-like for reading comfort
    background: '#F5F1EB',        // Warm off-white (sepia paper)
    backgroundSecondary: '#F0EAE2', // Slightly darker sepia
    backgroundTertiary: '#EBE3D9',  // Even warmer tone

    // Surface colors - subtle variations for depth
    surface: '#F8F4EE',           // Very light sepia for cards
    surfaceSecondary: '#F3EDE5',  // Medium sepia for elevated surfaces
    surfaceTertiary: '#EEE6DC',   // Darker sepia for contrast

    // Border colors - soft, barely visible
    border: '#E0D5C7',         // Soft brown border
    borderSecondary: '#D4C4B0', // Slightly more visible border

    // Primary colors - warm, comfortable
    primary: primaryLight,      // Warm brown
    primaryLight: '#A68B6B',    // Lighter brown
    primaryDark: '#6B5B4F',     // Darker brown

    // Secondary colors - earth tones
    secondary: secondaryLight,   // Muted gold
    secondaryLight: '#B8956F',  // Light gold
    secondaryDark: '#8B7355',   // Dark gold

    // Status colors - muted, less aggressive
    success: successLight,      // Muted green
    successLight: '#8BA578',    // Light green
    successDark: '#5A7A4A',     // Dark green
    error: errorLight,          // Muted red
    errorLight: '#C98A8A',      // Light red
    errorDark: '#A04A4A',       // Dark red
    warning: '#B8956F',         // Warm amber
    info: '#7A8B9A',           // Muted blue-gray

    // Legacy support
    tint: primaryLight,
    icon: '#6B5B4F',
    tabIconDefault: '#8B7D71',
    tabIconSelected: primaryLight,

    // Shadow colors - very subtle
    shadow: 'rgba(60, 46, 38, 0.08)',     // Warm shadow
    shadowDark: 'rgba(60, 46, 38, 0.15)', // Slightly darker warm shadow
  },
  dark: {
    // Text colors - warm, easy on eyes in dark mode
    text: '#E8DDD4',           // Warm light cream - gentle on eyes
    textSecondary: '#D4C4B0',  // Medium cream - for secondary text
    textMuted: '#B8A690',      // Muted cream - for less important text
    textInverse: '#2A1F1A',    // Dark brown - for light backgrounds

    // Background colors - dark but warm, not pure black
    background: '#1A1611',        // Very dark warm brown (not black)
    backgroundSecondary: '#2A1F1A', // Dark brown
    backgroundTertiary: '#3A2D24',  // Medium dark brown

    // Surface colors - layered warm dark tones
    surface: '#2A1F1A',           // Dark brown for cards
    surfaceSecondary: '#3A2D24',  // Medium brown for elevated surfaces
    surfaceTertiary: '#4A3B30',   // Lighter brown for highest elevation

    // Border colors - subtle warm borders
    border: '#4A3B30',         // Subtle brown border
    borderSecondary: '#5A4A3D', // More visible brown border

    // Primary colors - warm, comfortable in dark
    primary: '#A68B6B',         // Darker warm brown for headers
    primaryLight: '#D4B896',    // Soft cream for lighter elements
    primaryDark: '#8B7355',     // Even darker brown for depth

    // Secondary colors - warm gold tones
    secondary: secondaryDark,    // Light gold
    secondaryLight: '#F0D7B0',  // Very light gold
    secondaryDark: '#D4B896',   // Medium gold

    // Status colors - muted, comfortable in dark
    success: successDark,       // Soft green
    successLight: '#B8D4A8',    // Light green
    successDark: '#8BB88A',     // Medium green
    error: errorDark,           // Soft red
    errorLight: '#E6B3B3',      // Light red
    errorDark: '#D49999',       // Medium red
    warning: '#E6C79C',         // Warm amber
    info: '#A8B8C8',           // Muted blue-gray

    // Legacy support
    tint: primaryDark,
    icon: '#D4C4B0',
    tabIconDefault: '#B8A690',
    tabIconSelected: primaryDark,

    // Shadow colors - warm, subtle shadows
    shadow: 'rgba(26, 22, 17, 0.4)',     // Warm dark shadow
    shadowDark: 'rgba(26, 22, 17, 0.6)', // Deeper warm shadow
  },
};
