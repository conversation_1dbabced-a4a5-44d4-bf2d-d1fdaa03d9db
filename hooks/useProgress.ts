import { useCallback } from 'react';
import ProgressManager, { QuestionProgress } from '@/services/ProgressManager';

export interface UseProgressReturn {
  saveProgress: (
    fileUrl: string,
    title: string,
    currentQuestionIndex: number,
    totalQuestions: number,
    selectedSubject?: string,
    completedQuestions?: number[]
  ) => Promise<void>;
  getProgress: (fileUrl: string) => Promise<QuestionProgress | null>;
  clearProgress: (fileUrl: string) => Promise<void>;
  clearAllProgress: () => Promise<void>;
  getRecentProgress: (limit?: number) => Promise<QuestionProgress[]>;
  markQuestionCompleted: (fileUrl: string, questionIndex: number) => Promise<void>;
  getCompletionPercentage: (fileUrl: string) => Promise<number>;
}

export const useProgress = (): UseProgressReturn => {
  const progressManager = ProgressManager.getInstance();

  const saveProgress = useCallback(async (
    fileUrl: string,
    title: string,
    currentQuestionIndex: number,
    totalQuestions: number,
    selectedSubject: string = 'All',
    completedQuestions: number[] = []
  ) => {
    await progressManager.saveProgress(
      fileUrl,
      title,
      currentQuestionIndex,
      totalQuestions,
      selectedSubject,
      completedQuestions
    );
  }, [progressManager]);

  const getProgress = useCallback(async (fileUrl: string) => {
    return await progressManager.getProgress(fileUrl);
  }, [progressManager]);

  const clearProgress = useCallback(async (fileUrl: string) => {
    await progressManager.clearProgress(fileUrl);
  }, [progressManager]);

  const clearAllProgress = useCallback(async () => {
    await progressManager.clearAllProgress();
  }, [progressManager]);

  const getRecentProgress = useCallback(async (limit: number = 5) => {
    return await progressManager.getRecentProgress(limit);
  }, [progressManager]);

  const markQuestionCompleted = useCallback(async (fileUrl: string, questionIndex: number) => {
    await progressManager.markQuestionCompleted(fileUrl, questionIndex);
  }, [progressManager]);

  const getCompletionPercentage = useCallback(async (fileUrl: string) => {
    return await progressManager.getCompletionPercentage(fileUrl);
  }, [progressManager]);

  return {
    saveProgress,
    getProgress,
    clearProgress,
    clearAllProgress,
    getRecentProgress,
    markQuestionCompleted,
    getCompletionPercentage
  };
};
