import AsyncStorage from '@react-native-async-storage/async-storage';

export interface QuestionProgress {
  fileUrl: string;
  title: string;
  currentQuestionIndex: number;
  totalQuestions: number;
  selectedSubject: string;
  lastAccessedAt: string;
  completedQuestions: number[];
}

class ProgressManager {
  private static instance: ProgressManager;
  private readonly STORAGE_KEY = 'question_progress';

  public static getInstance(): ProgressManager {
    if (!ProgressManager.instance) {
      ProgressManager.instance = new ProgressManager();
    }
    return ProgressManager.instance;
  }

  /**
   * Generate a unique key for progress tracking
   */
  private generateProgressKey(fileUrl: string): string {
    const fileName = fileUrl.split('/').pop()?.replace('.json', '') || 'unknown';
    return fileName;
  }

  /**
   * Save progress for a specific question set
   */
  async saveProgress(
    fileUrl: string,
    title: string,
    currentQuestionIndex: number,
    totalQuestions: number,
    selectedSubject: string = 'All',
    completedQuestions: number[] = []
  ): Promise<void> {
    try {
      const progressKey = this.generateProgressKey(fileUrl);
      const allProgress = await this.getAllProgress();
      
      const progress: QuestionProgress = {
        fileUrl,
        title,
        currentQuestionIndex,
        totalQuestions,
        selectedSubject,
        lastAccessedAt: new Date().toISOString(),
        completedQuestions
      };

      allProgress[progressKey] = progress;
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(allProgress));

      console.log('✅ Progress saved:', {
        progressKey,
        currentQuestionIndex,
        totalQuestions,
        selectedSubject
      });
    } catch (error) {
      console.error('Error saving progress:', error);
    }
  }

  /**
   * Get progress for a specific question set
   */
  async getProgress(fileUrl: string): Promise<QuestionProgress | null> {
    try {
      const progressKey = this.generateProgressKey(fileUrl);
      const allProgress = await this.getAllProgress();
      
      const progress = allProgress[progressKey] || null;
      
      console.log('📖 Progress loaded:', {
        progressKey,
        found: !!progress,
        currentIndex: progress?.currentQuestionIndex
      });

      return progress;
    } catch (error) {
      console.error('Error getting progress:', error);
      return null;
    }
  }

  /**
   * Get all progress data
   */
  async getAllProgress(): Promise<{ [key: string]: QuestionProgress }> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Error getting all progress:', error);
      return {};
    }
  }

  /**
   * Clear progress for a specific question set
   */
  async clearProgress(fileUrl: string): Promise<void> {
    try {
      const progressKey = this.generateProgressKey(fileUrl);
      const allProgress = await this.getAllProgress();
      
      delete allProgress[progressKey];
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(allProgress));
      
      console.log('🗑️ Progress cleared for:', progressKey);
    } catch (error) {
      console.error('Error clearing progress:', error);
    }
  }

  /**
   * Clear all progress data
   */
  async clearAllProgress(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      console.log('🗑️ All progress cleared');
    } catch (error) {
      console.error('Error clearing all progress:', error);
    }
  }

  /**
   * Get recent progress (for continue learning feature)
   */
  async getRecentProgress(limit: number = 5): Promise<QuestionProgress[]> {
    try {
      const allProgress = await this.getAllProgress();
      const progressArray = Object.values(allProgress);
      
      // Sort by last accessed date (most recent first)
      const sortedProgress = progressArray.sort((a, b) => 
        new Date(b.lastAccessedAt).getTime() - new Date(a.lastAccessedAt).getTime()
      );

      return sortedProgress.slice(0, limit);
    } catch (error) {
      console.error('Error getting recent progress:', error);
      return [];
    }
  }

  /**
   * Mark a question as completed
   */
  async markQuestionCompleted(fileUrl: string, questionIndex: number): Promise<void> {
    try {
      const progress = await this.getProgress(fileUrl);
      if (!progress) return;

      const completedQuestions = [...progress.completedQuestions];
      if (!completedQuestions.includes(questionIndex)) {
        completedQuestions.push(questionIndex);
      }

      await this.saveProgress(
        progress.fileUrl,
        progress.title,
        progress.currentQuestionIndex,
        progress.totalQuestions,
        progress.selectedSubject,
        completedQuestions
      );
    } catch (error) {
      console.error('Error marking question as completed:', error);
    }
  }

  /**
   * Get completion percentage for a question set
   */
  async getCompletionPercentage(fileUrl: string): Promise<number> {
    try {
      const progress = await this.getProgress(fileUrl);
      if (!progress || progress.totalQuestions === 0) return 0;

      return Math.round((progress.completedQuestions.length / progress.totalQuestions) * 100);
    } catch (error) {
      console.error('Error calculating completion percentage:', error);
      return 0;
    }
  }
}

export default ProgressManager;
